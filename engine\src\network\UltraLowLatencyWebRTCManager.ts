/**
 * 超低延迟WebRTC管理器
 * 确保传输延迟稳定在30ms以下
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';

export interface LatencyTarget {
  total: number;        // 总延迟目标 (30ms)
  capture: number;      // 采集延迟目标 (5ms)
  encoding: number;     // 编码延迟目标 (8ms)
  network: number;      // 网络延迟目标 (10ms)
  decoding: number;     // 解码延迟目标 (5ms)
  rendering: number;    // 渲染延迟目标 (2ms)
}

export interface LatencyMeasurement {
  timestamp: number;
  total: number;
  breakdown: {
    capture: number;
    encoding: number;
    network: number;
    decoding: number;
    rendering: number;
    buffering: number;
  };
  targetAchieved: boolean;
}

export interface OptimizationAction {
  type: 'hardware' | 'software' | 'network' | 'quality';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  execute: () => Promise<void>;
  rollback?: () => Promise<void>;
}

export class UltraLowLatencyWebRTCManager extends EventEmitter {
  private peerConnection: RTCPeerConnection;
  private dataChannel: RTCDataChannel;
  private mediaStream: MediaStream;
  
  // 延迟目标和测量
  private latencyTarget: LatencyTarget;
  private latencyHistory: LatencyMeasurement[] = [];
  private currentOptimizations: Set<string> = new Set();
  
  // 性能监控
  private performanceMonitor: PerformanceMonitor;
  private latencyAnalyzer: LatencyAnalyzer;
  private optimizationEngine: OptimizationEngine;
  
  // 硬件加速
  private hardwareEncoder: VideoEncoder | null = null;
  private hardwareDecoder: VideoDecoder | null = null;
  private audioContext: AudioContext | null = null;
  
  // 网络优化
  private networkOptimizer: NetworkOptimizer;
  private congestionController: CongestionController;

  constructor() {
    super();
    
    this.latencyTarget = {
      total: 30,
      capture: 5,
      encoding: 8,
      network: 10,
      decoding: 5,
      rendering: 2
    };

    this.performanceMonitor = new PerformanceMonitor();
    this.latencyAnalyzer = new LatencyAnalyzer();
    this.optimizationEngine = new OptimizationEngine();
    this.networkOptimizer = new NetworkOptimizer();
    this.congestionController = new CongestionController();
  }

  async initialize(): Promise<void> {
    Debug.log('UltraLowLatencyWebRTCManager', '初始化超低延迟WebRTC管理器');

    // 1. 初始化硬件加速
    await this.initializeHardwareAcceleration();
    
    // 2. 创建优化的PeerConnection
    await this.createOptimizedPeerConnection();
    
    // 3. 设置超低延迟音频
    await this.setupUltraLowLatencyAudio();
    
    // 4. 启动性能监控
    this.startPerformanceMonitoring();
    
    // 5. 启动自动优化
    this.startAutoOptimization();

    Debug.log('UltraLowLatencyWebRTCManager', '初始化完成');
  }

  private async initializeHardwareAcceleration(): Promise<void> {
    try {
      // 检测硬件编码支持
      const encoderSupport = await VideoEncoder.isConfigSupported({
        codec: 'avc1.42001e',
        width: 1920,
        height: 1080,
        bitrate: 2000000,
        framerate: 60,
        hardwareAcceleration: 'prefer-hardware'
      });

      if (encoderSupport.supported) {
        this.hardwareEncoder = new VideoEncoder({
          output: (chunk, metadata) => this.handleEncodedChunk(chunk, metadata),
          error: (error) => Debug.error('UltraLowLatencyWebRTCManager', '硬件编码错误:', error)
        });

        await this.hardwareEncoder.configure({
          codec: 'avc1.42001e',
          width: 1920,
          height: 1080,
          bitrate: 2000000,
          framerate: 60,
          hardwareAcceleration: 'prefer-hardware',
          latencyMode: 'realtime',
          bitrateMode: 'constant'
        });

        Debug.log('UltraLowLatencyWebRTCManager', '硬件编码器初始化成功');
      }

      // 检测硬件解码支持
      const decoderSupport = await VideoDecoder.isConfigSupported({
        codec: 'avc1.42001e',
        hardwareAcceleration: 'prefer-hardware'
      });

      if (decoderSupport.supported) {
        this.hardwareDecoder = new VideoDecoder({
          output: (frame) => this.handleDecodedFrame(frame),
          error: (error) => Debug.error('UltraLowLatencyWebRTCManager', '硬件解码错误:', error)
        });

        await this.hardwareDecoder.configure({
          codec: 'avc1.42001e',
          hardwareAcceleration: 'prefer-hardware'
        });

        Debug.log('UltraLowLatencyWebRTCManager', '硬件解码器初始化成功');
      }

    } catch (error) {
      Debug.warn('UltraLowLatencyWebRTCManager', '硬件加速初始化失败，使用软件编解码:', error);
    }
  }

  private async createOptimizedPeerConnection(): Promise<void> {
    const configuration: RTCConfiguration = {
      iceServers: [
        { urls: 'stun:stun.local.dl-engine.com:3478' },
        { urls: 'stun:stun.l.google.com:19302' }
      ],
      
      // 超低延迟优化配置
      iceTransportPolicy: 'all',
      bundlePolicy: 'max-bundle',
      rtcpMuxPolicy: 'require',
      iceCandidatePoolSize: 10,
      
      // 启用DSCP标记
      enableDscp: true
    };

    this.peerConnection = new RTCPeerConnection(configuration);
    
    // 设置事件监听器
    this.setupPeerConnectionListeners();
    
    // 创建超低延迟数据通道
    this.dataChannel = this.peerConnection.createDataChannel('ultra-low-latency', {
      ordered: false,           // 无序传输
      maxRetransmits: 0,       // 不重传
      priority: 'high'         // 高优先级
    });

    this.setupDataChannelListeners();
  }

  private async setupUltraLowLatencyAudio(): Promise<void> {
    this.audioContext = new AudioContext({
      latencyHint: 'interactive',
      sampleRate: 48000
    });

    // 加载超低延迟音频处理器
    await this.audioContext.audioWorklet.addModule('/audio-worklets/ultra-low-latency-processor.js');
    
    const audioWorklet = new AudioWorkletNode(this.audioContext, 'ultra-low-latency-processor', {
      processorOptions: {
        bufferSize: 64,  // 64样本 ≈ 1.33ms @ 48kHz
        latencyMode: 'ultra-low'
      }
    });

    audioWorklet.connect(this.audioContext.destination);
    
    Debug.log('UltraLowLatencyWebRTCManager', `音频延迟: ${this.audioContext.baseLatency * 1000}ms`);
  }

  private startPerformanceMonitoring(): void {
    // 每10ms测量一次延迟
    setInterval(async () => {
      const measurement = await this.measureLatency();
      this.latencyHistory.push(measurement);
      
      // 保持最近1000个测量值
      if (this.latencyHistory.length > 1000) {
        this.latencyHistory.shift();
      }

      // 发送延迟更新事件
      this.emit('latencyUpdate', measurement);

      // 检查是否需要优化
      if (!measurement.targetAchieved) {
        await this.triggerOptimization(measurement);
      }

    }, 10);
  }

  private async measureLatency(): Promise<LatencyMeasurement> {
    const startTime = performance.now();
    
    // 测量各个环节的延迟
    const breakdown = {
      capture: await this.measureCaptureLatency(),
      encoding: await this.measureEncodingLatency(),
      network: await this.measureNetworkLatency(),
      decoding: await this.measureDecodingLatency(),
      rendering: await this.measureRenderingLatency(),
      buffering: await this.measureBufferingLatency()
    };

    const total = Object.values(breakdown).reduce((sum, val) => sum + val, 0);
    const targetAchieved = total <= this.latencyTarget.total;

    return {
      timestamp: startTime,
      total,
      breakdown,
      targetAchieved
    };
  }

  private async measureCaptureLatency(): Promise<number> {
    // 测量音视频采集延迟
    if (this.mediaStream) {
      const videoTrack = this.mediaStream.getVideoTracks()[0];
      if (videoTrack) {
        const settings = videoTrack.getSettings();
        // 基于帧率估算采集延迟
        return settings.frameRate ? 1000 / settings.frameRate : 16.67; // 默认60fps
      }
    }
    return 5; // 默认5ms
  }

  private async measureEncodingLatency(): Promise<number> {
    // 如果使用硬件编码器，延迟更低
    return this.hardwareEncoder ? 3 : 8;
  }

  private async measureNetworkLatency(): Promise<number> {
    return new Promise((resolve) => {
      const startTime = performance.now();
      const pingData = new ArrayBuffer(8);
      const view = new Float64Array(pingData);
      view[0] = startTime;

      const timeout = setTimeout(() => resolve(50), 100); // 超时默认50ms

      const handlePong = (event: MessageEvent) => {
        const pongTime = performance.now();
        const pingTime = new Float64Array(event.data)[0];
        const rtt = pongTime - pingTime;
        
        clearTimeout(timeout);
        this.dataChannel.removeEventListener('message', handlePong);
        resolve(rtt / 2); // 单向延迟
      };

      this.dataChannel.addEventListener('message', handlePong);
      this.dataChannel.send(pingData);
    });
  }

  private async measureDecodingLatency(): Promise<number> {
    // 如果使用硬件解码器，延迟更低
    return this.hardwareDecoder ? 2 : 5;
  }

  private async measureRenderingLatency(): Promise<number> {
    // 测量渲染延迟
    return 2; // 通常2ms左右
  }

  private async measureBufferingLatency(): Promise<number> {
    // 测量缓冲延迟
    if (this.audioContext) {
      return (this.audioContext.baseLatency + this.audioContext.outputLatency) * 1000;
    }
    return 0;
  }

  private async triggerOptimization(measurement: LatencyMeasurement): Promise<void> {
    const analysis = this.latencyAnalyzer.analyze(measurement, this.latencyTarget);
    const actions = this.optimizationEngine.generateActions(analysis);

    Debug.log('UltraLowLatencyWebRTCManager', `延迟超标: ${measurement.total}ms，执行优化`);

    // 按优先级执行优化动作
    for (const action of actions.sort((a, b) => this.getPriorityValue(b.priority) - this.getPriorityValue(a.priority))) {
      if (!this.currentOptimizations.has(action.type)) {
        try {
          await action.execute();
          this.currentOptimizations.add(action.type);
          Debug.log('UltraLowLatencyWebRTCManager', `执行优化: ${action.description}`);
        } catch (error) {
          Debug.error('UltraLowLatencyWebRTCManager', `优化失败: ${action.description}`, error);
        }
      }
    }
  }

  private getPriorityValue(priority: string): number {
    const values = { low: 1, medium: 2, high: 3, critical: 4 };
    return values[priority as keyof typeof values] || 0;
  }

  private startAutoOptimization(): void {
    // 自动优化策略
    setInterval(() => {
      this.performAutoOptimization();
    }, 1000); // 每秒检查一次
  }

  private async performAutoOptimization(): Promise<void> {
    const recentMeasurements = this.latencyHistory.slice(-10);
    if (recentMeasurements.length === 0) return;

    const averageLatency = recentMeasurements.reduce((sum, m) => sum + m.total, 0) / recentMeasurements.length;
    const successRate = recentMeasurements.filter(m => m.targetAchieved).length / recentMeasurements.length;

    // 如果成功率低于80%，触发优化
    if (successRate < 0.8) {
      await this.performPreventiveOptimization(averageLatency);
    }

    // 如果延迟持续上升，触发预防性优化
    const trend = this.calculateLatencyTrend(recentMeasurements);
    if (trend > 2) { // 延迟上升超过2ms
      await this.performPreventiveOptimization(averageLatency);
    }
  }

  private calculateLatencyTrend(measurements: LatencyMeasurement[]): number {
    if (measurements.length < 5) return 0;
    
    const firstHalf = measurements.slice(0, Math.floor(measurements.length / 2));
    const secondHalf = measurements.slice(Math.floor(measurements.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, m) => sum + m.total, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, m) => sum + m.total, 0) / secondHalf.length;
    
    return secondAvg - firstAvg;
  }

  private async performPreventiveOptimization(currentLatency: number): Promise<void> {
    Debug.log('UltraLowLatencyWebRTCManager', '执行预防性优化');

    // 预防性优化策略
    const optimizations = [
      () => this.optimizeNetworkPath(),
      () => this.adjustEncodingParameters(),
      () => this.optimizeBufferSizes(),
      () => this.enableAdaptiveBitrate()
    ];

    for (const optimization of optimizations) {
      try {
        await optimization();
      } catch (error) {
        Debug.error('UltraLowLatencyWebRTCManager', '预防性优化失败:', error);
      }
    }
  }

  // 优化方法实现
  private async optimizeNetworkPath(): Promise<void> {
    await this.networkOptimizer.selectOptimalPath();
  }

  private async adjustEncodingParameters(): Promise<void> {
    if (this.hardwareEncoder) {
      // 调整编码参数以降低延迟
      await this.hardwareEncoder.configure({
        codec: 'avc1.42001e',
        width: 1920,
        height: 1080,
        bitrate: 1500000, // 降低码率
        framerate: 60,
        hardwareAcceleration: 'prefer-hardware',
        latencyMode: 'realtime'
      });
    }
  }

  private async optimizeBufferSizes(): Promise<void> {
    // 优化缓冲区大小
    if (this.audioContext) {
      // 重新创建更小的缓冲区
      await this.setupUltraLowLatencyAudio();
    }
  }

  private async enableAdaptiveBitrate(): Promise<void> {
    await this.congestionController.enableAdaptiveBitrate();
  }

  // 事件处理
  private setupPeerConnectionListeners(): void {
    this.peerConnection.oniceconnectionstatechange = () => {
      Debug.log('UltraLowLatencyWebRTCManager', `ICE连接状态: ${this.peerConnection.iceConnectionState}`);
    };

    this.peerConnection.onconnectionstatechange = () => {
      Debug.log('UltraLowLatencyWebRTCManager', `连接状态: ${this.peerConnection.connectionState}`);
    };
  }

  private setupDataChannelListeners(): void {
    this.dataChannel.onopen = () => {
      Debug.log('UltraLowLatencyWebRTCManager', '数据通道已打开');
      this.emit('dataChannelOpen');
    };

    this.dataChannel.onmessage = (event) => {
      this.handleDataChannelMessage(event);
    };
  }

  private handleDataChannelMessage(event: MessageEvent): void {
    // 处理接收到的数据
    this.emit('dataReceived', event.data);
  }

  private handleEncodedChunk(chunk: EncodedVideoChunk, metadata: any): void {
    // 处理编码后的视频块
    this.emit('videoEncoded', chunk, metadata);
  }

  private handleDecodedFrame(frame: VideoFrame): void {
    // 处理解码后的视频帧
    this.emit('videoDecoded', frame);
    frame.close();
  }

  // 公共API
  async sendData(data: ArrayBuffer): Promise<void> {
    if (this.dataChannel && this.dataChannel.readyState === 'open') {
      this.dataChannel.send(data);
    }
  }

  getLatencyStatistics(): any {
    const recent = this.latencyHistory.slice(-100);
    if (recent.length === 0) return null;

    const latencies = recent.map(m => m.total);
    const sorted = [...latencies].sort((a, b) => a - b);

    return {
      current: recent[recent.length - 1]?.total || 0,
      average: latencies.reduce((sum, val) => sum + val, 0) / latencies.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
      targetAchievementRate: recent.filter(m => m.targetAchieved).length / recent.length
    };
  }

  close(): void {
    if (this.dataChannel) {
      this.dataChannel.close();
    }
    if (this.peerConnection) {
      this.peerConnection.close();
    }
    if (this.audioContext) {
      this.audioContext.close();
    }
    if (this.hardwareEncoder) {
      this.hardwareEncoder.close();
    }
    if (this.hardwareDecoder) {
      this.hardwareDecoder.close();
    }
  }
}

// 辅助类的简化实现
class PerformanceMonitor {
  // 性能监控实现
}

class LatencyAnalyzer {
  analyze(measurement: LatencyMeasurement, target: LatencyTarget): any {
    // 延迟分析实现
    return {};
  }
}

class OptimizationEngine {
  generateActions(analysis: any): OptimizationAction[] {
    // 优化动作生成实现
    return [];
  }
}

class NetworkOptimizer {
  async selectOptimalPath(): Promise<void> {
    // 网络路径优化实现
  }
}

class CongestionController {
  async enableAdaptiveBitrate(): Promise<void> {
    // 自适应码率实现
  }
}
